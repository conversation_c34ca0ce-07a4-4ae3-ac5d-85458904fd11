<template>
  <div class="lakeTable">
    <div
      class="btn"
      v-if="permission.hasButton(['catalogConfig_lakeEdit', 'catalogConfig_lakeExport'])"
    >
      <ElButton
        v-if="permission.hasButton('catalogConfig_lakeEdit')"
        @click="handleEdit"
        type="primary"
        >编辑目录</ElButton
      >
      <ElButton
        v-if="permission.hasButton('catalogConfig_lakeExport')"
        @click="handleDownload"
        type="primary"
        >导出目录
      </ElButton>
    </div>
    <div class="table-main">
      <div class="title">
        <ElButton
          v-if="permission.hasButton('catalogConfig_lakeVersion')"
          @click="handleVersion"
          type="primary"
          link
        >
          版本管理</ElButton
        >
        <el-text v-if="versionData" style="font-weight: 600"
          >V{{ versionData.version }}版本</el-text
        >
        <div v-if="versionData">
          <el-text class="text">申请人：{{ versionData.applyUname }}</el-text>
          <el-text class="text"> 审核人：{{ versionData.authUname }}</el-text>
          <el-text class="text">发布时间：{{ versionData.releaseTime }}</el-text>
        </div>
      </div>
      <div class="table">
        <el-table
          v-loading="loading"
          header-cell-class-name="common-table-header"
          cell-class-name="common-table-cell"
          ref="multipleTableRef"
          :data="tableData"
          height="100%"
          style="width: 100%"
          row-key="tid"
          :tree-props="treeProps"
          default-expand-all
        >
          <el-table-column property="catalogAlias" label="目录名称" show-overflow-tooltip />
          <el-table-column property="level" label="目录级别">
            <template #default="scope">{{ scope.row.bsType === 1 ? '菜单' : '数据库' }}</template>
          </el-table-column>
          <el-table-column property="catalogDesc" label="描述" show-overflow-tooltip />
          <el-table-column property="createTime" label="创建时间" show-overflow-tooltip />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElButton, ElLoading, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import usePageData from '../usePageData'
import { dataCatalogQueryUseApi, exportDataLakeCatalogApi } from '@/api/lake_catalog'
import { ref } from 'vue'
import useUserInfo from '@/store/useUserInfo'
import dayjs from 'dayjs'

const permission = useUserInfo()

const router = useRouter()

const { treeProps, tableData, loading } = usePageData(2)

const versionData = ref<any>()
const getVersion = async () => {
  try {
    const { data, message, status } = await dataCatalogQueryUseApi()
    if ([200].includes(status)) {
      delete data?.dataCatalogApplyRelationVOList
      versionData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

getVersion()

// 下载
const handleDownload = async () => {
  const loading = ElLoading.service({
    text: '正在下载模板，请耐心等待！',
    background: 'rgba(0, 0, 0, 0.8)',
  })
  try {
    const res: any = await exportDataLakeCatalogApi()
    const timeStr = dayjs().format('YYYY-MM-DD HH-mm-ss')
    const fileName = `目录-${timeStr}.xlsx`

    // 检查浏览器是否支持文件系统访问API
    if ('showSaveFilePicker' in window) {
      try {
        const fileHandle = await (window as any).showSaveFilePicker({
          suggestedName: fileName,
          types: [
            {
              description: 'Excel文件',
              accept: { 'application/vnd.ms-excel': ['.xlsx'] },
            },
          ],
        })
        const writable = await fileHandle.createWritable()
        await writable.write(res.data)
        await writable.close()
        ElMessage.success('文件保存成功')
      } catch (saveError) {
        // 用户取消保存或其他错误，回退到默认下载方式
        if ((saveError as Error).name !== 'AbortError') {
          fallbackDownload(res.data, fileName)
        }
      }
    } else {
      // 浏览器不支持文件系统访问API，使用默认下载方式
      fallbackDownload(res.data, fileName)
    }
    loading.close()
  } catch {
    loading.close()
    ElMessage.error('导出失败')
  }
}

// 回退的下载方式
const fallbackDownload = (data: any, fileName: string) => {
  const url = URL.createObjectURL(new Blob([data], { type: 'application/vnd.ms-excel' }))
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', fileName)
  document.body.appendChild(link)
  link.click()
  link.remove()
  URL.revokeObjectURL(url)
}

const handleVersion = () => {
  router.push({ name: 'catalogVersion' })
}

const handleEdit = () => {
  router.push({ name: 'editCatalog' })
}
</script>

<style lang="less" scoped>
.lakeTable {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-main {
  flex: 1;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 0 15px 15px 15px;
  background-color: @withe;

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;

    .text + .text {
      margin-left: 15px;
    }
  }

  .table {
    flex: 1;
    overflow: hidden;
    box-sizing: border-box;
  }
}

.btn {
  text-align: right;
  background-color: @withe;
  margin-bottom: 15px;
  padding: 10px 15px;
}
</style>
