<template>
  <div class="data-statistics">
    <div class="item">
      <div class="echarts" ref="heatEchartsRef"></div>
    </div>
    <div class="item">
      <div class="echarts" ref="clusterEchartsRef"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { EChartsOption } from 'echarts'
// import Echarts from '@/components/Echarts/index.vue'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { transform } from 'echarts-stat'

// 热力图相关变量
let heatEcharts: any = null
const heatEchartsRef = ref<HTMLElement>()
const heatOptions = ref<EChartsOption>()

// 聚类图相关变量
let clusterEcharts: any = null
const clusterEchartsRef = ref<HTMLElement>()

// 热力图数据生成
let noise = getNoiseHelper()
let xData = []
let yData = []
noise.seed(Math.random())
function generateData(theta, min, max) {
  let data = []
  for (let i = 0; i <= 200; i++) {
    for (let j = 0; j <= 100; j++) {
      data.push([i, j, noise.perlin2(i / 40, j / 20) + 0.5])
    }
    xData.push(i)
  }
  for (let j = 0; j < 100; j++) {
    yData.push(j)
  }
  console.log('%c [ data ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', 'data', data)
  return data
}
let heatData = generateData(2, -5, 5)
heatOptions.value = {
  tooltip: {},
  grid: {
    right: 140,
    left: 40,
  },
  xAxis: {
    type: 'category',
    data: xData,
  },
  yAxis: {
    type: 'category',
    data: yData,
  },
  visualMap: {
    type: 'piecewise',
    min: 0,
    max: 1,
    left: 'right',
    top: 'center',
    calculable: true,
    realtime: false,
    splitNumber: 8,
    inRange: {
      color: [
        '#313695',
        '#4575b4',
        '#74add1',
        '#abd9e9',
        '#e0f3f8',
        '#ffffbf',
        '#fee090',
        '#fdae61',
        '#f46d43',
        '#d73027',
        '#a50026',
      ],
    },
  },
  series: [
    {
      name: 'Gaussian',
      type: 'heatmap',
      data: heatData,
      emphasis: {
        itemStyle: {
          borderColor: '#333',
          borderWidth: 1,
        },
      },
      progressive: 1000,
      animation: false,
    },
  ],
}

function getNoiseHelper() {
  class Grad {
    constructor(x, y, z) {
      this.x = x
      this.y = y
      this.z = z
    }
    dot2(x, y) {
      return this.x * x + this.y * y
    }
    dot3(x, y, z) {
      return this.x * x + this.y * y + this.z * z
    }
  }
  const grad3 = [
    new Grad(1, 1, 0),
    new Grad(-1, 1, 0),
    new Grad(1, -1, 0),
    new Grad(-1, -1, 0),
    new Grad(1, 0, 1),
    new Grad(-1, 0, 1),
    new Grad(1, 0, -1),
    new Grad(-1, 0, -1),
    new Grad(0, 1, 1),
    new Grad(0, -1, 1),
    new Grad(0, 1, -1),
    new Grad(0, -1, -1),
  ]
  const p = [
    151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69,
    142, 8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219,
    203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175,
    74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230,
    220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76,
    132, 187, 208, 89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186,
    3, 64, 52, 217, 226, 250, 124, 123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59,
    227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154, 163, 70,
    221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232, 178,
    185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81,
    51, 145, 235, 249, 14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115,
    121, 50, 45, 127, 4, 150, 254, 138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195,
    78, 66, 215, 61, 156, 180,
  ]
  // To remove the need for index wrapping, double the permutation table length
  let perm = new Array(512)
  let gradP = new Array(512)
  // This isn't a very good seeding function, but it works ok. It supports 2^16
  // different seed values. Write something better if you need more seeds.
  function seed(seed) {
    if (seed > 0 && seed < 1) {
      // Scale the seed out
      seed *= 65536
    }
    seed = Math.floor(seed)
    if (seed < 256) {
      seed |= seed << 8
    }
    for (let i = 0; i < 256; i++) {
      let v
      if (i & 1) {
        v = p[i] ^ (seed & 255)
      } else {
        v = p[i] ^ ((seed >> 8) & 255)
      }
      perm[i] = perm[i + 256] = v
      gradP[i] = gradP[i + 256] = grad3[v % 12]
    }
  }
  seed(0)
  // ##### Perlin noise stuff
  function fade(t) {
    return t * t * t * (t * (t * 6 - 15) + 10)
  }
  function lerp(a, b, t) {
    return (1 - t) * a + t * b
  }
  // 2D Perlin Noise
  function perlin2(x, y) {
    // Find unit grid cell containing point
    let X = Math.floor(x),
      Y = Math.floor(y)
    // Get relative xy coordinates of point within that cell
    x = x - X
    y = y - Y
    // Wrap the integer cells at 255 (smaller integer period can be introduced here)
    X = X & 255
    Y = Y & 255
    // Calculate noise contributions from each of the four corners
    let n00 = gradP[X + perm[Y]].dot2(x, y)
    let n01 = gradP[X + perm[Y + 1]].dot2(x, y - 1)
    let n10 = gradP[X + 1 + perm[Y]].dot2(x - 1, y)
    let n11 = gradP[X + 1 + perm[Y + 1]].dot2(x - 1, y - 1)
    // Compute the fade curve value for x
    let u = fade(x)
    // Interpolate the four results
    return lerp(lerp(n00, n10, u), lerp(n01, n11, u), fade(y))
  }
  return {
    seed,
    perlin2,
  }
}

// 初始化热力图
const initHeatChart = () => {
  if (heatEchartsRef.value) {
    heatEcharts = echarts.init(heatEchartsRef.value as HTMLElement)
    heatEcharts.setOption(heatOptions.value, true)
  }
}

// 初始化聚类图
const initClusterChart = () => {
  if (clusterEchartsRef.value) {
    clusterEcharts = echarts.init(clusterEchartsRef.value as HTMLElement)

    echarts.registerTransform(transform.clustering)
    const clusterData = [
      [3.275154, 2.957587],
      [-3.344465, 2.603513],
      [0.355083, -3.376585],
      [1.852435, 3.547351],
      [-2.078973, 2.552013],
      [-0.993756, -0.884433],
      [2.682252, 4.007573],
      [-3.087776, 2.878713],
      [-1.565978, -1.256985],
      [2.441611, 0.444826],
      [-0.659487, 3.111284],
      [-0.459601, -2.618005],
      [2.17768, 2.387793],
      [-2.920969, 2.917485],
      [-0.028814, -4.168078],
      [3.625746, 2.119041],
      [-3.912363, 1.325108],
      [-0.551694, -2.814223],
      [2.855808, 3.483301],
      [-3.594448, 2.856651],
      [0.421993, -2.372646],
      [1.650821, 3.407572],
      [-2.082902, 3.384412],
      [-0.718809, -2.492514],
      [4.513623, 3.841029],
      [-4.822011, 4.607049],
      [-0.656297, -1.449872],
      [1.919901, 4.439368],
      [-3.287749, 3.918836],
      [-1.576936, -2.977622],
      [3.598143, 1.97597],
      [-3.977329, 4.900932],
      [-1.79108, -2.184517],
      [3.914654, 3.559303],
      [-1.910108, 4.166946],
      [-1.226597, -3.317889],
      [1.148946, 3.345138],
      [-2.113864, 3.548172],
      [0.845762, -3.589788],
      [2.629062, 3.535831],
      [-1.640717, 2.990517],
      [-1.881012, -2.485405],
      [4.606999, 3.510312],
      [-4.366462, 4.023316],
      [0.765015, -3.00127],
      [3.121904, 2.173988],
      [-4.025139, 4.65231],
      [-0.559558, -3.840539],
      [4.376754, 4.863579],
      [-1.874308, 4.032237],
      [-0.089337, -3.026809],
      [3.997787, 2.518662],
      [-3.082978, 2.884822],
      [0.845235, -3.454465],
      [1.327224, 3.358778],
      [-2.889949, 3.596178],
      [-0.966018, -2.839827],
      [2.960769, 3.079555],
      [-3.275518, 1.577068],
      [0.639276, -3.41284],
    ]

    const CLUSTER_COUNT = 6
    const DIENSIION_CLUSTER_INDEX = 2
    const COLOR_ALL = ['#37A2DA', '#e06343', '#37a354', '#b55dba', '#b5bd48', '#8378EA', '#96BFFF']
    const pieces = []
    for (let i = 0; i < CLUSTER_COUNT; i++) {
      pieces.push({
        value: i,
        label: 'cluster ' + i,
        color: COLOR_ALL[i],
      })
    }

    const clusterOption = {
      dataset: [
        {
          source: clusterData,
        },
        {
          transform: {
            type: 'ecStat:clustering',
            config: {
              clusterCount: CLUSTER_COUNT,
              outputType: 'single',
              outputClusterIndexDimension: DIENSIION_CLUSTER_INDEX,
            },
          },
        },
      ],
      tooltip: {
        position: 'top',
      },
      visualMap: {
        type: 'piecewise',
        top: 'middle',
        min: 0,
        max: CLUSTER_COUNT,
        left: 10,
        splitNumber: CLUSTER_COUNT,
        dimension: DIENSIION_CLUSTER_INDEX,
        pieces: pieces,
      },
      grid: {
        left: 120,
      },
      xAxis: {},
      yAxis: {},
      series: {
        type: 'scatter',
        encode: { tooltip: [0, 1] },
        symbolSize: 15,
        itemStyle: {
          borderColor: '#555',
        },
        datasetIndex: 1,
      },
    }

    clusterEcharts.setOption(clusterOption, true)
  }
}

// 窗口大小调整处理
const handleResize = () => {
  heatEcharts?.resize()
  clusterEcharts?.resize()
}

onMounted(() => {
  // 初始化两个图表
  initHeatChart()
  initClusterChart()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  heatEcharts?.dispose()
  clusterEcharts?.dispose()
})
</script>
<style scoped lang="less">
.data-statistics {
  margin: 0 0.5208vw;
  display: flex;
  box-sizing: border-box;
  .item {
    height: 15vw;
    flex: 1;
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    padding: 0.5vw;
    overflow: hidden;
    display: flex;
    align-items: center;
    font-size: 12px;
    .echarts {
      position: relative;
      width: 100%;
      height: 100%;
    }
  }
  .item + .item {
    margin-left: 0.5208vw;
  }
}
</style>
