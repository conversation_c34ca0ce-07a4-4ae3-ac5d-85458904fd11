<template>
  <div class="data-statistics">
    <div class="item">
      <Echarts v-if="heatOptions" :options="heatOptions" />
    </div>
    <div class="item">
      <Echarts v-if="clusterOptions" :options="clusterOptions" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { EChartsOption } from 'echarts'
import Echarts from '@/components/Echarts/index.vue'
import { ref } from 'vue'
import * as echarts from 'echarts'

// 热力图和聚类图的options
const heatOptions = ref<EChartsOption>()
const clusterOptions = ref<EChartsOption>()

// 热力图数据生成 - 月份 vs 数据类型
const months = [
  '1月',
  '2月',
  '3月',
  '4月',
  '5月',
  '6月',
  '7月',
  '8月',
  '9月',
  '10月',
  '11月',
  '12月',
]
const dataTypes = [
  '成果影像',
  '原始影像',
  '影像瓦片',
  '高程数据',
  '高程瓦片',
  '矢量数据',
  '矢量瓦片',
  'BIM模型',
  '点云',
  '倾斜摄影',
  '全景影像',
]

// 生成热力图数据 - 月份 vs 数据类型，数值表示数据量
function generateHeatmapData() {
  const data = []
  for (let i = 0; i < months.length; i++) {
    for (let j = 0; j < dataTypes.length; j++) {
      // 生成随机数据量，模拟不同数据类型在不同月份的分布
      const value = Math.floor(Math.random() * 100) + 10 // 10-109的数据量
      data.push([i, j, value]) // [月份索引, 数据类型索引, 数据量]
    }
  }
  return data
}

// 生成热力图配置
const generateHeatOptions = () => {
  const heatData = generateHeatmapData()
  return {
    tooltip: {
      position: 'top',
      formatter: function (params: any) {
        if (params && params.data) {
          const monthIndex = params.data[0]
          const typeIndex = params.data[1]
          const value = params.data[2]
          return `月份: ${months[monthIndex]}<br/>类型: ${dataTypes[typeIndex]}<br/>数据量: ${value}`
        }
        return ''
      },
    },
    grid: {
      bottom: 25,
      top: 25,
      left: 60,
      right: 60,
    },
    xAxis: {
      type: 'category',
      data: months,
      splitArea: {
        show: true,
      },
      axisLabel: {
        fontSize: 10,
      },
    },
    yAxis: {
      type: 'category',
      data: dataTypes,
      axisLabel: {
        fontSize: 10,
      },
      splitArea: {
        show: true,
      },
    },
    visualMap: {
      min: 10,
      max: 109,
      calculable: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      itemWidth: 15,
      itemHeight: 120,
      textStyle: {
        fontSize: 10,
      },
      inRange: {
        color: ['#ffffff', '#ffeaa7', '#fdcb6e', '#e17055', '#e74c3c'],
      },
    },
    series: [
      {
        name: '数据统计',
        type: 'heatmap',
        data: heatData.filter((item) => item[2] > 0), // 过滤掉数值为0的数据点
        label: {
          show: false, // 隐藏数值标签，只在鼠标悬浮时通过tooltip显示
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }
}

// 生成聚类图配置
const generateClusterOptions = () => {
  const clusterData = []
  for (let i = 0; i < months.length; i++) {
    for (let j = 0; j <= 20; j++) {
      // 随机选择一个数据类型
      const typeIndex = Math.floor(Math.random() * dataTypes.length)
      // 随机决定是否在这个位置放置数据点
      if (Math.random() > 0.8) {
        clusterData.push([
          i + Math.random() * 0.8 - 0.4, // X坐标：月份索引 + 小幅随机偏移
          j + Math.random() * 0.8 - 0.4, // Y坐标：数值 + 小幅随机偏移
          typeIndex, // 数据类型索引（用于颜色映射）
          i, // 月份索引（用于tooltip）
          j, // 数值（用于tooltip）
        ])
      }
    }
  }

  return {
    tooltip: {
      position: 'top',
      formatter: function (params: any) {
        if (params && params.data) {
          const typeIndex = params.data[2]
          const monthIndex = params.data[3]
          const value = params.data[4]
          return `类型: ${dataTypes[typeIndex]}<br/>月份: ${months[monthIndex]}<br/>数值: ${value}`
        }
        return ''
      },
    },
    visualMap: {
      type: 'piecewise',
      min: 0,
      max: dataTypes.length - 1,
      dimension: 2,
      orient: 'vertical',
      left: 5,
      top: 'center',
      itemWidth: 15,
      itemHeight: 10,
      textStyle: {
        fontSize: 10,
      },
      itemGap: 5,
      pieces: dataTypes.map((type, index) => ({
        value: index,
        label: type,
        color: [
          '#313695',
          '#4575b4',
          '#74add1',
          '#abd9e9',
          '#e0f3f8',
          '#ffffbf',
          '#fee090',
          '#fdae61',
          '#f46d43',
          '#d73027',
          '#a50026',
        ][index],
      })),
    },
    grid: {
      bottom: 10,
      top: 40,
      left: 80,
      right: 20,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLabel: {
        fontSize: 10,
        interval: 0, // 显示所有月份
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e0e0e0',
          type: 'dashed',
        },
      },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 20,
      axisLabel: {
        fontSize: 10,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e0e0e0',
          type: 'dashed',
        },
      },
    },
    series: {
      type: 'scatter',
      data: clusterData,
      symbolSize: function () {
        return Math.random() * 15 + 5 // 随机大小 5-20
      },
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 1,
        opacity: 0.8,
      },
      emphasis: {
        itemStyle: {
          borderWidth: 2,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
        },
      },
    },
  }
}

// 初始化配置
heatOptions.value = generateHeatOptions()
clusterOptions.value = generateClusterOptions()
</script>
<style scoped lang="less">
.data-statistics {
  margin: 0 0.5208vw;
  display: flex;
  box-sizing: border-box;
  .item {
    height: 13vw;
    flex: 1;
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    padding: 0.5vw;
    overflow: hidden;
    display: flex;
    align-items: center;
    font-size: 12px;
    .echarts {
      position: relative;
      width: 100%;
      height: 100%;
    }
  }
  .item + .item {
    margin-left: 0.5208vw;
  }
}
</style>
