<template>
  <div class="data-statistics">
    <div class="item">
      <div class="echarts" ref="heatEchartsRef"></div>
    </div>
    <div class="item">
      <div class="echarts" ref="clusterEchartsRef"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { EChartsOption } from 'echarts'
// import Echarts from '@/components/Echarts/index.vue'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { transform } from 'echarts-stat'

// 热力图相关变量
let heatEcharts: any = null
const heatEchartsRef = ref<HTMLElement>()
const heatOptions = ref<EChartsOption>()

// 聚类图相关变量
let clusterEcharts: any = null
const clusterEchartsRef = ref<HTMLElement>()

// 热力图数据生成 - 月份 vs 数据类型
const months = [
  '1月',
  '2月',
  '3月',
  '4月',
  '5月',
  '6月',
  '7月',
  '8月',
  '9月',
  '10月',
  '11月',
  '12月',
]
const dataTypes = [
  '成果影像',
  '原始影像',
  '影像瓦片',
  '高程数据',
  '高程瓦片',
  '矢量数据',
  '矢量瓦片',
  'BIM模型',
  '点云',
  '倾斜摄影',
  '全景影像',
]

// 生成热力图数据
function generateHeatmapData() {
  const data = []
  for (let i = 0; i < months.length; i++) {
    for (let j = 0; j < dataTypes.length; j++) {
      // 生成0-20之间的随机数据量
      const value = Math.floor(Math.random() * 21)
      data.push([i, j, value])
    }
  }
  return data
}

let heatData = generateHeatmapData()
heatOptions.value = {
  tooltip: {
    position: 'top',
    formatter: function (params: any) {
      if (params && params.data) {
        const monthIndex = params.data[0]
        const typeIndex = params.data[1]
        const value = params.data[2]
        return `${months[monthIndex]}<br/>${dataTypes[typeIndex]}<br/>数据量: ${value}`
      }
      return ''
    },
  },
  grid: {
    bottom: 20,
    top: 10,
    left: 50,
    right: 50,
  },
  xAxis: {
    type: 'category',
    data: months,
    splitArea: {
      show: true,
    },
    axisLabel: {
      fontSize: 10,
    },
  },
  yAxis: {
    type: 'category',
    data: dataTypes,
    splitArea: {
      show: true,
    },
    axisLabel: {
      fontSize: 10,
    },
  },
  visualMap: {
    min: 1,
    max: 20,
    calculable: true,
    orient: 'vertical',
    left: 'right',
    top: 'center',
    itemWidth: 15,
    itemHeight: 120,
    textStyle: {
      fontSize: 10,
    },
    inRange: {
      color: ['#ffffff', '#ffeaa7', '#fdcb6e', '#e17055', '#e74c3c'],
    },
  },
  series: [
    {
      name: '数据统计',
      type: 'heatmap',
      data: heatData.filter((item) => item[2] > 0), // 过滤掉数值为0的数据点
      label: {
        show: false, // 隐藏数值标签，只在鼠标悬浮时通过tooltip显示
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    },
  ],
}

// 初始化热力图
const initHeatChart = () => {
  if (heatEchartsRef.value) {
    heatEcharts = echarts.init(heatEchartsRef.value as HTMLElement)
    heatEcharts.setOption(heatOptions.value, true)
  }
}

// 初始化聚类图
const initClusterChart = () => {
  if (clusterEchartsRef.value) {
    clusterEcharts = echarts.init(clusterEchartsRef.value as HTMLElement)

    // 生成散点数据：X轴为月份，Y轴为数据类型
    const clusterData = []
    for (let i = 0; i < dataTypes.length; i++) {
      for (let j = 0; j < months.length; j++) {
        // 为每种数据类型在每个月份生成1-3个数据点
        const pointCount = Math.floor(Math.random() * 3) + 1
        for (let k = 0; k < pointCount; k++) {
          clusterData.push([
            j + Math.random() * 0.8 - 0.4, // X坐标：月份索引 + 小幅随机偏移
            i + Math.random() * 0.8 - 0.4, // Y坐标：数据类型索引 + 小幅随机偏移
            i, // 数据类型索引（用于颜色映射）
          ])
        }
      }
    }

    const TYPE_COUNT = dataTypes.length
    const DIMENSION_TYPE_INDEX = 2
    const TYPE_COLORS = [
      '#313695',
      '#4575b4',
      '#74add1',
      '#abd9e9',
      '#e0f3f8',
      '#ffffbf',
      '#fee090',
      '#fdae61',
      '#f46d43',
      '#d73027',
      '#a50026',
    ]
    const pieces = []
    for (let i = 0; i < TYPE_COUNT; i++) {
      pieces.push({
        value: i,
        label: dataTypes[i],
        color: TYPE_COLORS[i],
      })
    }

    const clusterOption = {
      tooltip: {
        position: 'top',
        formatter: function (params: any) {
          if (params && params.data) {
            const monthIndex = Math.round(params.data[0])
            const typeIndex = Math.round(params.data[1])
            return `${months[monthIndex]}<br/>${dataTypes[typeIndex]}`
          }
          return ''
        },
      },
      visualMap: {
        type: 'piecewise',
        top: 'center',
        min: 0,
        max: TYPE_COUNT - 1,
        left: 5, // 图例放在左边
        splitNumber: TYPE_COUNT,
        dimension: DIMENSION_TYPE_INDEX,
        pieces: pieces,
        itemWidth: 10, // 减小图例宽度
        itemHeight: 6, // 减小图例高度
        itemGap: 2, // 减小图例项之间的间距
        textStyle: {
          fontSize: 9,
        },
      },
      grid: {
        left: 120, // 减少左边距
        right: 20, // 减少右边距
        top: 20, // 减少上边距
        bottom: 40, // 增加下边距以容纳月份标签
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: months,
        axisLabel: {
          fontSize: 10,
          interval: 0, // 显示所有月份
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#e0e0e0',
            type: 'dashed',
          },
        },
      },
      yAxis: {
        type: 'category',
        data: dataTypes,
        axisLabel: {
          fontSize: 10,
          interval: 0, // 显示所有数据类型
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#e0e0e0',
            type: 'dashed',
          },
        },
      },
      series: {
        type: 'scatter',
        data: clusterData,
        symbolSize: function () {
          return Math.random() * 15 + 5 // 随机大小 5-20
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1,
          opacity: 0.8,
        },
        emphasis: {
          itemStyle: {
            borderWidth: 2,
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
          },
        },
      },
    }

    clusterEcharts.setOption(clusterOption, true)
  }
}

// 窗口大小调整处理
const handleResize = () => {
  heatEcharts?.resize()
  clusterEcharts?.resize()
}

onMounted(() => {
  // 初始化两个图表
  initHeatChart()
  initClusterChart()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  heatEcharts?.dispose()
  clusterEcharts?.dispose()
})
</script>
<style scoped lang="less">
.data-statistics {
  margin: 0 0.5208vw;
  display: flex;
  box-sizing: border-box;
  .item {
    height: 13vw;
    flex: 1;
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    padding: 0.5vw;
    overflow: hidden;
    display: flex;
    align-items: center;
    font-size: 12px;
    .echarts {
      position: relative;
      width: 100%;
      height: 100%;
    }
  }
  .item + .item {
    margin-left: 0.5208vw;
  }
}
</style>
