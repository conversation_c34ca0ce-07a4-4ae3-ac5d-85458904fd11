<template>
  <div class="data-statistics">
    <div class="item">
      <div class="echarts" ref="heatEchartsRef"></div>
    </div>
    <div class="item">
      <div class="echarts" ref="clusterEchartsRef"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { EChartsOption } from 'echarts'
// import Echarts from '@/components/Echarts/index.vue'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { transform } from 'echarts-stat'

// 热力图相关变量
let heatEcharts: any = null
const heatEchartsRef = ref<HTMLElement>()
const heatOptions = ref<EChartsOption>()

// 聚类图相关变量
let clusterEcharts: any = null
const clusterEchartsRef = ref<HTMLElement>()

// 热力图数据生成 - 月份 vs 数据类型
const months = [
  '1月',
  '2月',
  '3月',
  '4月',
  '5月',
  '6月',
  '7月',
  '8月',
  '9月',
  '10月',
  '11月',
  '12月',
]
const dataTypes = [
  '成果影像',
  '原始影像',
  '影像瓦片',
  '高程数据',
  '高程瓦片',
  '矢量数据',
  '矢量瓦片',
  'BIM模型',
  '点云',
  '倾斜摄影',
  '全景影像',
]

// 生成热力图数据
function generateHeatmapData() {
  const data = []
  for (let i = 0; i < months.length; i++) {
    for (let j = 0; j < dataTypes.length; j++) {
      // 生成0-20之间的随机数据量
      const value = Math.floor(Math.random() * 21)
      data.push([i, j, value])
    }
  }
  return data
}

let heatData = generateHeatmapData()
heatOptions.value = {
  tooltip: {
    position: 'top',
    formatter: function (params: any) {
      if (params && params.data) {
        const monthIndex = params.data[0]
        const typeIndex = params.data[1]
        const value = params.data[2]
        return `${months[monthIndex]}<br/>${dataTypes[typeIndex]}<br/>数据量: ${value}`
      }
      return ''
    },
  },
  grid: {
    bottom: 20,
    top: 10,
    left: 50,
    right: 50,
  },
  xAxis: {
    type: 'category',
    data: months,
    splitArea: {
      show: true,
    },
    axisLabel: {
      fontSize: 10,
    },
  },
  yAxis: {
    type: 'category',
    data: dataTypes,
    splitArea: {
      show: true,
    },
    axisLabel: {
      fontSize: 10,
    },
  },
  visualMap: {
    min: 0,
    max: 20,
    calculable: true,
    orient: 'vertical',
    left: 'right',
    top: 'center',
    itemWidth: 15,
    itemHeight: 120,
    textStyle: {
      fontSize: 10,
    },
    inRange: {
      color: [
        '#313695',
        '#4575b4',
        '#74add1',
        '#abd9e9',
        '#e0f3f8',
        '#ffffbf',
        '#fee090',
        '#fdae61',
        '#f46d43',
        '#d73027',
        '#a50026',
      ],
    },
  },
  series: [
    {
      name: '数据统计',
      type: 'heatmap',
      data: heatData,
      label: {
        show: false, // 隐藏数值标签，只在鼠标悬浮时通过tooltip显示
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    },
  ],
}

// 初始化热力图
const initHeatChart = () => {
  if (heatEchartsRef.value) {
    heatEcharts = echarts.init(heatEchartsRef.value as HTMLElement)
    heatEcharts.setOption(heatOptions.value, true)
  }
}

// 初始化聚类图
const initClusterChart = () => {
  if (clusterEchartsRef.value) {
    clusterEcharts = echarts.init(clusterEchartsRef.value as HTMLElement)

    echarts.registerTransform(transform.clustering)
    const clusterData = [
      [3.275154, 2.957587],
      [-3.344465, 2.603513],
      [0.355083, -3.376585],
      [1.852435, 3.547351],
      [-2.078973, 2.552013],
      [-0.993756, -0.884433],
      [2.682252, 4.007573],
      [-3.087776, 2.878713],
      [-1.565978, -1.256985],
      [2.441611, 0.444826],
      [-0.659487, 3.111284],
      [-0.459601, -2.618005],
      [2.17768, 2.387793],
      [-2.920969, 2.917485],
      [-0.028814, -4.168078],
      [3.625746, 2.119041],
      [-3.912363, 1.325108],
      [-0.551694, -2.814223],
      [2.855808, 3.483301],
      [-3.594448, 2.856651],
      [0.421993, -2.372646],
      [1.650821, 3.407572],
      [-2.082902, 3.384412],
      [-0.718809, -2.492514],
      [4.513623, 3.841029],
      [-4.822011, 4.607049],
      [-0.656297, -1.449872],
      [1.919901, 4.439368],
      [-3.287749, 3.918836],
      [-1.576936, -2.977622],
      [3.598143, 1.97597],
      [-3.977329, 4.900932],
      [-1.79108, -2.184517],
      [3.914654, 3.559303],
      [-1.910108, 4.166946],
      [-1.226597, -3.317889],
      [1.148946, 3.345138],
      [-2.113864, 3.548172],
      [0.845762, -3.589788],
      [2.629062, 3.535831],
      [-1.640717, 2.990517],
      [-1.881012, -2.485405],
      [4.606999, 3.510312],
      [-4.366462, 4.023316],
      [0.765015, -3.00127],
      [3.121904, 2.173988],
      [-4.025139, 4.65231],
      [-0.559558, -3.840539],
      [4.376754, 4.863579],
      [-1.874308, 4.032237],
      [-0.089337, -3.026809],
      [3.997787, 2.518662],
      [-3.082978, 2.884822],
      [0.845235, -3.454465],
      [1.327224, 3.358778],
      [-2.889949, 3.596178],
      [-0.966018, -2.839827],
      [2.960769, 3.079555],
      [-3.275518, 1.577068],
      [0.639276, -3.41284],
    ]

    const CLUSTER_COUNT = 6
    const DIENSIION_CLUSTER_INDEX = 2
    const COLOR_ALL = ['#37A2DA', '#e06343', '#37a354', '#b55dba', '#b5bd48', '#8378EA', '#96BFFF']
    const pieces = []
    for (let i = 0; i < CLUSTER_COUNT; i++) {
      pieces.push({
        value: i,
        label: 'cluster ' + i,
        color: COLOR_ALL[i],
      })
    }

    const clusterOption = {
      dataset: [
        {
          source: clusterData,
        },
        {
          transform: {
            type: 'ecStat:clustering',
            config: {
              clusterCount: CLUSTER_COUNT,
              outputType: 'single',
              outputClusterIndexDimension: DIENSIION_CLUSTER_INDEX,
            },
          },
        },
      ],
      tooltip: {
        position: 'top',
        formatter: function (params: any) {
          if (params && params.data) {
            return `X: ${params.data[0].toFixed(2)}<br/>Y: ${params.data[1].toFixed(2)}<br/>聚类: ${params.data[2]}`
          }
          return ''
        },
      },
      visualMap: {
        type: 'piecewise',
        top: 'center',
        min: 0,
        max: CLUSTER_COUNT,
        left: 5, // 减少左边距，给图表更多空间
        splitNumber: CLUSTER_COUNT,
        dimension: DIENSIION_CLUSTER_INDEX,
        pieces: pieces,
        itemWidth: 12, // 减小图例宽度
        itemHeight: 8, // 减小图例高度
        textStyle: {
          fontSize: 10,
        },
      },
      grid: {
        left: 120, // 减少左边距
        right: 10, // 减少右边距
        top: 10, // 减少上边距
        bottom: 10, // 减少下边距
        containLabel: true,
      },
      xAxis: {
        axisLabel: {
          fontSize: 10,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        axisLabel: {
          fontSize: 10,
        },
        axisTick: {
          show: false,
        },
      },
      series: {
        type: 'scatter',
        encode: { tooltip: [0, 1] },
        symbolSize: 12, // 稍微减小点的大小，让图表看起来更紧凑
        itemStyle: {
          borderColor: '#555',
          borderWidth: 1,
        },
        datasetIndex: 1,
      },
    }

    clusterEcharts.setOption(clusterOption, true)
  }
}

// 窗口大小调整处理
const handleResize = () => {
  heatEcharts?.resize()
  clusterEcharts?.resize()
}

onMounted(() => {
  // 初始化两个图表
  initHeatChart()
  initClusterChart()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  heatEcharts?.dispose()
  clusterEcharts?.dispose()
})
</script>
<style scoped lang="less">
.data-statistics {
  margin: 0 0.5208vw;
  display: flex;
  box-sizing: border-box;
  .item {
    height: 13vw;
    flex: 1;
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    padding: 0.5vw;
    overflow: hidden;
    display: flex;
    align-items: center;
    font-size: 12px;
    .echarts {
      position: relative;
      width: 100%;
      height: 100%;
    }
  }
  .item + .item {
    margin-left: 0.5208vw;
  }
}
</style>
