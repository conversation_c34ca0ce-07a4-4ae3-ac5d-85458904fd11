<template>
  <div class="CatalogConfig">
    <div class="breadcrumb">
      <ElBreadcrumb separator="/">
        <ElBreadcrumbItem @click="router.go(-2)" class="link">数据目录</ElBreadcrumbItem>
        <ElBreadcrumbItem @click="router.go(-1)" class="link">目录配置</ElBreadcrumbItem>
        <ElBreadcrumbItem>编辑目录</ElBreadcrumbItem>
      </ElBreadcrumb>
    </div>
    <div class="tableData">
      <MainLayout>
        <template #header>
          <div class="btns">
            <ElButton @click="AddEditDialogRef?.handleOpen(null, 0)" type="primary"
              >新建一级目录</ElButton
            >
            <ElUpload
              class="ElUpload"
              :action="importDataLakeCatalogApi()"
              :show-file-list="false"
              accept=".xls,.xlsx,.csv,.xml"
              :on-success="handleFileSuccess"
              :before-upload="beforeUpload"
              :headers="{ token: permission.token }"
            >
              <ElButton type="primary">导入目录</ElButton>
            </ElUpload>
            <ElButton :loading="btnLoading" @click="ApplyDialogRef?.handleOpen()" type="primary"
              >提交审核</ElButton
            >
            <ElButton @click="getTableData" type="primary">刷新</ElButton>
          </div>
        </template>
        <div class="table-tree">
          <ul class="custom-tree-node node-title">
            <li class="node-label">目录名称</li>
            <ul class="node-list">
              <li class="node-item" style="width: 250px">分类扩展码</li>
              <li class="node-item" style="width: 100px">目录级别</li>
              <li class="node-item" style="width: 250px">描述</li>
              <li class="node-item" style="width: 200px">创建时间</li>
              <li class="node-item" style="width: 100px">编辑标记</li>
              <li class="node-item" style="width: 100px">操作</li>
            </ul>
          </ul>
          <div class="table-tree-loading" v-loading="loading">
            <ElScrollbar height="100%">
              <ElTree
                style="box-sizing: border-box"
                :data="tableData"
                draggable
                default-expand-all
                node-key="tid"
                :props="{
                  children: 'childVOList',
                  label: 'catalogAlias',
                }"
                @node-drop="handleDrop"
              >
                <template #default="{ node, data }">
                  <ul class="custom-tree-node">
                    <li class="node-label">
                      {{ node.label }}
                    </li>
                    <ul class="node-list">
                      <li class="node-item" style="width: 250px">
                        {{ '0'.repeat(node.level) }}-{{ data.tid }}
                      </li>
                      <li class="node-item" style="width: 100px">
                        {{ data.bsType === 1 ? '菜单' : '数据库' }}
                      </li>
                      <li class="node-item" style="width: 250px">{{ data.catalogDesc }}</li>
                      <li class="node-item" style="width: 200px">{{ data.createTime }}</li>
                      <li class="node-item" style="width: 100px">
                        <ElTag v-if="data.editFlag === 2" effect="dark" type="success">新增</ElTag>
                        <ElTag v-else-if="data.editFlag === 3" effect="dark" type="warning"
                          >修改</ElTag
                        >
                        <ElTag v-else-if="data.editFlag === 4" effect="dark" type="danger"
                          >删除</ElTag
                        >
                        <ElTag v-else effect="dark" type="primary">默认</ElTag>
                      </li>
                      <li class="node-item" style="width: 130px">
                        <template v-if="![4].includes(data.editFlag)">
                          <el-button
                            @click="AddEditDialogRef?.handleOpen(data, 2)"
                            title="新建子目录"
                            class="common-icon-btn"
                            type="primary"
                            plain
                            link
                          >
                            <template #icon>
                              <SvgIcon name="CirclePlus" />
                            </template>
                          </el-button>
                          <el-button
                            @click="AddEditDialogRef?.handleOpen(data, 1)"
                            title="编辑"
                            class="common-icon-btn"
                            type="primary"
                            plain
                            link
                          >
                            <template #icon>
                              <SvgIcon name="edit" />
                            </template>
                          </el-button>
                          <el-button
                            @click="handleDelete(data)"
                            title="删除"
                            class="common-icon-btn"
                            type="danger"
                            plain
                            link
                          >
                            <template #icon>
                              <SvgIcon name="delete" />
                            </template>
                          </el-button>
                        </template>
                      </li>
                    </ul>
                  </ul>
                </template>
              </ElTree>
            </ElScrollbar>
          </div>
        </div>
        <AddEditDialog @handle-add="handleAdd" ref="AddEditDialogRef" />
        <ApplyDialog :tableData ref="ApplyDialogRef" />
      </MainLayout>
    </div>
  </div>
</template>
<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import {
  ElBreadcrumb,
  ElBreadcrumbItem,
  ElButton,
  ElMessage,
  ElMessageBox,
  ElScrollbar,
  ElTree,
  ElUpload,
} from 'element-plus'
import { useRouter } from 'vue-router'
import usePageData from '../usePageData'
import AddEditDialog from './AddEditDialog.vue'
import { ref } from 'vue'
import ApplyDialog from './ApplyDialog.vue'
import { importDataLakeCatalogApi } from '@/api/lake_catalog'
import useUserInfo from '@/store/useUserInfo'
import * as XLSX from 'xlsx'

const permission = useUserInfo()

const router = useRouter()

const handleDrop = (node: any) => {
  console.log(node.data, 'sssssssss')
  dropUpdate(node.data)
}

const dropUpdate = (row: any) => {
  if (row.childVOList && row.childVOList.length) {
    row.childVOList.forEach((item: any) => {
      dropUpdate(item)
    })
  }
  row.editFlag = 3
  row.ptid = ''
}

const AddEditDialogRef = ref<InstanceType<typeof AddEditDialog>>()

const ApplyDialogRef = ref<InstanceType<typeof ApplyDialog>>()

const handleFileSuccess = (response: any) => {
  const { status, message, data } = response
  if ([200].includes(status)) {
    ElMessage.success('导入成功！')
    tableData.value = data
  } else {
    ElMessage.error(message)
  }
  console.log(response)
}

const beforeUpload = async (rawFile: any) => {
  const allowedExtensions = ['.xls', '.xlsx', '.csv', '.xml']
  const isValidFormat = allowedExtensions.some((ext) => rawFile.name.toLowerCase().endsWith(ext))

  if (!isValidFormat) {
    ElMessage.warning('请上传xls、xlsx、csv或xml格式的文件')
    return false
  }

  // 如果是CSV或XML文件，需要转换为Excel格式
  if (rawFile.name.toLowerCase().endsWith('.csv') || rawFile.name.toLowerCase().endsWith('.xml')) {
    try {
      const convertedFile = await convertToExcel(rawFile)
      // 创建新的File对象替换原文件
      const newFile = new File([convertedFile], rawFile.name.replace(/\.(csv|xml)$/i, '.xlsx'), {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })

      // 替换rawFile的属性
      Object.defineProperty(rawFile, 'name', {
        value: newFile.name,
        writable: false,
      })
      Object.defineProperty(rawFile, 'type', {
        value: newFile.type,
        writable: false,
      })
      Object.defineProperty(rawFile, 'arrayBuffer', {
        value: () => newFile.arrayBuffer(),
        writable: false,
      })
      Object.defineProperty(rawFile, 'stream', {
        value: () => newFile.stream(),
        writable: false,
      })
    } catch (error) {
      ElMessage.error('文件转换失败，请检查文件格式')
      return false
    }
  }

  return true
}

// 将CSV或XML文件转换为Excel格式
const convertToExcel = async (file: File): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        let workbook: XLSX.WorkBook
        const data = e.target?.result as string

        if (file.name.toLowerCase().endsWith('.csv')) {
          // 处理CSV文件
          workbook = XLSX.read(data, { type: 'string' })
        } else if (file.name.toLowerCase().endsWith('.xml')) {
          // 处理XML文件
          const parser = new DOMParser()
          const xmlDoc = parser.parseFromString(data, 'text/xml')

          // 检查是否有解析错误
          if (xmlDoc.getElementsByTagName('parsererror').length > 0) {
            throw new Error('XML格式错误')
          }

          // 简单的XML到表格转换
          const rows = xmlDoc.getElementsByTagName('row') || xmlDoc.getElementsByTagName('item')
          const worksheet_data = []

          if (rows.length > 0) {
            // 提取表头
            const firstRow = rows[0]
            const headers = Array.from(firstRow.children).map((child) => child.tagName)
            worksheet_data.push(headers)

            // 提取数据
            for (let i = 0; i < rows.length; i++) {
              const row = rows[i]
              const rowData = Array.from(row.children).map((child) => child.textContent || '')
              worksheet_data.push(rowData)
            }
          } else {
            // 如果没有标准的行结构，尝试提取所有文本内容
            const allElements = xmlDoc.getElementsByTagName('*')
            worksheet_data.push(['标签名', '内容'])

            for (let i = 0; i < allElements.length; i++) {
              const element = allElements[i]
              if (element.textContent && element.children.length === 0) {
                worksheet_data.push([element.tagName, element.textContent])
              }
            }
          }

          const worksheet = XLSX.utils.aoa_to_sheet(worksheet_data)
          workbook = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
        } else {
          throw new Error('不支持的文件格式')
        }

        // 将workbook转换为Excel文件
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
        const blob = new Blob([excelBuffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        })

        resolve(blob)
      } catch (error) {
        reject(error)
      }
    }

    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsText(file, 'utf-8')
  })
}

const handleAdd = (row: any) => {
  tableData.value.push(row)
}

const btnLoading = ref<boolean>(false)

const { tableData, getTableData, loading } = usePageData(2)

const handleDelete = async (row: any) => {
  const hasFiles = checkForFiles(row)

  ElMessageBox.confirm(
    hasFiles
      ? '当前目录下有挂载资源，是否确定删除？'
      : '删除时同步删除该目录下的资源，是否确定删除？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    handleDel(row)
  })
}

const checkForFiles = (row: any): boolean => {
  if (row.fileCount && row.fileCount > 0) {
    return true
  }

  if (row.childVOList && row.childVOList.length) {
    for (const child of row.childVOList) {
      if (checkForFiles(child)) {
        return true
      }
    }
  }

  return false
}

const handleDel = (row: any) => {
  if (row.childVOList && row.childVOList.length) {
    row.childVOList.forEach((item: any) => {
      handleDel(item)
    })
  }
  row.editFlag = 4
}
</script>
<style lang="less" scoped>
.table-tree {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .table-tree-loading {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
  }
}

.custom-tree-node {
  height: 40px;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .node-label {
    max-width: 280px;
    width: 100%;
    min-width: 200px;
    .ellipseLine();
  }

  .node-list {
    display: flex;
    box-sizing: border-box;
    align-items: center;
  }

  .node-item {
    line-height: 40px;
    .ellipseLine();
  }
}

.node-title {
  padding-left: 24px;
  padding-right: 30px;
}

.CatalogConfig {
  :deep(.el-tree-node__content) {
    height: 40px;
  }

  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0 20px 20px 20px;

  .breadcrumb {
    height: 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    line-height: 40px;

    .link {
      :deep(.el-breadcrumb__inner) {
        color: #303133;
        font-weight: bold;
        cursor: pointer;

        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }

  .tableData {
    padding: 15px;
    flex: 1;
    overflow: auto;
    box-sizing: border-box;

    .btns {
      display: flex;
      justify-content: flex-end;

      .ElUpload {
        margin: 0 12px;
      }
    }
  }
}
</style>
