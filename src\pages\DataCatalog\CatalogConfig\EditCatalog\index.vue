<template>
  <div class="CatalogConfig">
    <div class="breadcrumb">
      <ElBreadcrumb separator="/">
        <ElBreadcrumbItem @click="router.go(-2)" class="link">数据目录</ElBreadcrumbItem>
        <ElBreadcrumbItem @click="router.go(-1)" class="link">目录配置</ElBreadcrumbItem>
        <ElBreadcrumbItem>编辑目录</ElBreadcrumbItem>
      </ElBreadcrumb>
    </div>
    <div class="tableData">
      <MainLayout>
        <template #header>
          <div class="btns">
            <ElButton @click="AddEditDialogRef?.handleOpen(null, 0)" type="primary"
              >新建一级目录</ElButton
            >
            <ElUpload
              class="ElUpload"
              :show-file-list="false"
              accept=".xls,.xlsx,.csv,.xml"
              :before-upload="beforeUpload"
              :http-request="customUpload"
            >
              <ElButton type="primary">导入目录</ElButton>
            </ElUpload>
            <ElButton :loading="btnLoading" @click="ApplyDialogRef?.handleOpen()" type="primary"
              >提交审核</ElButton
            >
            <ElButton @click="getTableData" type="primary">刷新</ElButton>
          </div>
        </template>
        <div class="table-tree">
          <ul class="custom-tree-node node-title">
            <li class="node-label">目录名称</li>
            <ul class="node-list">
              <li class="node-item" style="width: 250px">分类扩展码</li>
              <li class="node-item" style="width: 100px">目录级别</li>
              <li class="node-item" style="width: 250px">描述</li>
              <li class="node-item" style="width: 200px">创建时间</li>
              <li class="node-item" style="width: 100px">编辑标记</li>
              <li class="node-item" style="width: 100px">操作</li>
            </ul>
          </ul>
          <div class="table-tree-loading" v-loading="loading">
            <ElScrollbar height="100%">
              <ElTree
                style="box-sizing: border-box"
                :data="tableData"
                draggable
                default-expand-all
                node-key="tid"
                :props="{
                  children: 'childVOList',
                  label: 'catalogAlias',
                }"
                @node-drop="handleDrop"
              >
                <template #default="{ node, data }">
                  <ul class="custom-tree-node">
                    <li class="node-label">
                      {{ node.label }}
                    </li>
                    <ul class="node-list">
                      <li class="node-item" style="width: 250px">
                        {{ '0'.repeat(node.level) }}-{{ data.tid }}
                      </li>
                      <li class="node-item" style="width: 100px">
                        {{ data.bsType === 1 ? '菜单' : '数据库' }}
                      </li>
                      <li class="node-item" style="width: 250px">{{ data.catalogDesc }}</li>
                      <li class="node-item" style="width: 200px">{{ data.createTime }}</li>
                      <li class="node-item" style="width: 100px">
                        <ElTag v-if="data.editFlag === 2" effect="dark" type="success">新增</ElTag>
                        <ElTag v-else-if="data.editFlag === 3" effect="dark" type="warning"
                          >修改</ElTag
                        >
                        <ElTag v-else-if="data.editFlag === 4" effect="dark" type="danger"
                          >删除</ElTag
                        >
                        <ElTag v-else effect="dark" type="primary">默认</ElTag>
                      </li>
                      <li class="node-item" style="width: 130px">
                        <template v-if="![4].includes(data.editFlag)">
                          <el-button
                            @click="AddEditDialogRef?.handleOpen(data, 2)"
                            title="新建子目录"
                            class="common-icon-btn"
                            type="primary"
                            plain
                            link
                          >
                            <template #icon>
                              <SvgIcon name="CirclePlus" />
                            </template>
                          </el-button>
                          <el-button
                            @click="AddEditDialogRef?.handleOpen(data, 1)"
                            title="编辑"
                            class="common-icon-btn"
                            type="primary"
                            plain
                            link
                          >
                            <template #icon>
                              <SvgIcon name="edit" />
                            </template>
                          </el-button>
                          <el-button
                            @click="handleDelete(data)"
                            title="删除"
                            class="common-icon-btn"
                            type="danger"
                            plain
                            link
                          >
                            <template #icon>
                              <SvgIcon name="delete" />
                            </template>
                          </el-button>
                        </template>
                      </li>
                    </ul>
                  </ul>
                </template>
              </ElTree>
            </ElScrollbar>
          </div>
        </div>
        <AddEditDialog @handle-add="handleAdd" ref="AddEditDialogRef" />
        <ApplyDialog :tableData ref="ApplyDialogRef" />
      </MainLayout>
    </div>
  </div>
</template>
<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import {
  ElBreadcrumb,
  ElBreadcrumbItem,
  ElButton,
  ElMessage,
  ElMessageBox,
  ElScrollbar,
  ElTree,
  ElUpload,
} from 'element-plus'
import { useRouter } from 'vue-router'
import usePageData from '../usePageData'
import AddEditDialog from './AddEditDialog.vue'
import { ref } from 'vue'
import ApplyDialog from './ApplyDialog.vue'
import { importDataLakeCatalogApi } from '@/api/lake_catalog'
import useUserInfo from '@/store/useUserInfo'
import * as XLSX from 'xlsx'

const permission = useUserInfo()

const router = useRouter()

const handleDrop = (node: any) => {
  console.log(node.data, 'sssssssss')
  dropUpdate(node.data)
}

const dropUpdate = (row: any) => {
  if (row.childVOList && row.childVOList.length) {
    row.childVOList.forEach((item: any) => {
      dropUpdate(item)
    })
  }
  row.editFlag = 3
  row.ptid = ''
}

const AddEditDialogRef = ref<InstanceType<typeof AddEditDialog>>()

const ApplyDialogRef = ref<InstanceType<typeof ApplyDialog>>()

const handleFileSuccess = (response: any) => {
  const { status, message, data } = response
  if ([200].includes(status)) {
    ElMessage.success('导入成功！')
    tableData.value = data
  } else {
    ElMessage.error(message)
  }
  console.log(response)
}

const beforeUpload = (rawFile: any) => {
  const allowedExtensions = ['.xls', '.xlsx', '.csv', '.xml']
  const isValidFormat = allowedExtensions.some((ext) => rawFile.name.toLowerCase().endsWith(ext))

  if (!isValidFormat) {
    ElMessage.warning('请上传xls、xlsx、csv或xml格式的文件')
    return false
  }

  return true
}

// 自定义上传函数
const customUpload = async (options: any) => {
  const { file } = options

  console.log('开始处理文件:', file.name, file.type, file.size)

  try {
    let uploadFile = file

    // 如果是CSV或XML文件，需要转换为Excel格式
    if (file.name.toLowerCase().endsWith('.csv') || file.name.toLowerCase().endsWith('.xml')) {
      console.log('检测到CSV/XML文件，开始数据转换...')

      // 读取并解析文件数据
      const jsonData = await parseFileToJson(file)
      console.log('解析得到的数据:', jsonData)

      // 将JSON数据转换为Excel文件
      const excelBlob = await jsonToExcel(jsonData, file.name)
      uploadFile = new File([excelBlob], file.name.replace(/\.(csv|xml)$/i, '.xlsx'), {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })
      console.log('文件转换完成:', uploadFile.name, uploadFile.type, uploadFile.size)
    }

    // 创建FormData
    const formData = new FormData()
    formData.append('file', uploadFile)

    console.log('发送上传请求到:', importDataLakeCatalogApi())

    // 发送请求
    const response = await fetch(importDataLakeCatalogApi(), {
      method: 'POST',
      headers: {
        token: permission.token,
      },
      body: formData,
    })

    console.log('上传响应状态:', response.status, response.statusText)

    const result = await response.json()
    console.log('上传响应结果:', result)

    // 处理响应
    handleFileSuccess(result)
  } catch (error) {
    ElMessage.error('文件处理失败，请检查文件格式')
    console.error('Upload error:', error)
  }
}

// 解析文件为JSON数据
const parseFileToJson = async (file: File): Promise<any[]> => {
  console.log('开始解析文件为JSON:', file.name)

  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const data = e.target?.result as string
        console.log('文件内容长度:', data.length)

        if (file.name.toLowerCase().endsWith('.csv')) {
          console.log('解析CSV文件...')
          const jsonData = parseCSVToJson(data)
          resolve(jsonData)
        } else if (file.name.toLowerCase().endsWith('.xml')) {
          console.log('解析XML文件...')
          const jsonData = parseXMLToJson(data)
          resolve(jsonData)
        } else {
          throw new Error('不支持的文件格式')
        }
      } catch (error) {
        console.error('文件解析错误:', error)
        reject(error)
      }
    }

    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsText(file, 'utf-8')
  })
}

// CSV转JSON - 改进版本，处理复杂CSV格式
const parseCSVToJson = (csvData: string): any[] => {
  const lines = csvData
    .trim()
    .split('\n')
    .filter((line) => line.trim() !== '')
  if (lines.length === 0) return []

  console.log('CSV总行数:', lines.length)

  // 更健壮的CSV解析
  const parseCSVLine = (line: string): string[] => {
    const result = []
    let current = ''
    let inQuotes = false

    for (let i = 0; i < line.length; i++) {
      const char = line[i]

      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }

    result.push(current.trim())
    return result
  }

  // 解析表头
  const headers = parseCSVLine(lines[0]).map((header) => header.replace(/"/g, ''))
  console.log('CSV表头:', headers)

  // 限制数据行数，避免过大文件导致栈溢出
  const maxRows = Math.min(lines.length - 1, 1000) // 最多处理1000行数据
  console.log('将处理数据行数:', maxRows)

  // 解析数据行
  const jsonData = []
  for (let i = 1; i <= maxRows; i++) {
    if (i >= lines.length) break

    const values = parseCSVLine(lines[i]).map((value) => value.replace(/"/g, ''))

    // 确保数据完整性
    if (values.length !== headers.length) {
      console.warn(`第${i}行数据列数不匹配，跳过该行`)
      continue
    }

    const rowObject: any = {}
    headers.forEach((header, index) => {
      // 清理数据，避免特殊字符导致问题
      const cleanValue = (values[index] || '').toString().trim()
      rowObject[header] = cleanValue
    })

    jsonData.push(rowObject)
  }

  console.log('CSV解析完成，有效数据行数:', jsonData.length)
  console.log('CSV解析结果示例:', jsonData.slice(0, 2)) // 只显示前2行
  return jsonData
}

// XML转JSON - 改进版本，添加安全检查
const parseXMLToJson = (xmlData: string): any[] => {
  // 限制XML文件大小，避免过大文件导致问题
  if (xmlData.length > 1024 * 1024) {
    // 1MB限制
    throw new Error('XML文件过大，请使用小于1MB的文件')
  }

  const parser = new DOMParser()
  const xmlDoc = parser.parseFromString(xmlData, 'text/xml')

  // 检查是否有解析错误
  if (xmlDoc.getElementsByTagName('parsererror').length > 0) {
    throw new Error('XML格式错误')
  }

  const jsonData = []

  // 尝试多种XML结构
  const rows = xmlDoc.getElementsByTagName('row')
  const items = xmlDoc.getElementsByTagName('item')
  const records = xmlDoc.getElementsByTagName('record')

  let targetElements = rows
  if (rows.length === 0 && items.length > 0) {
    targetElements = items
  } else if (rows.length === 0 && items.length === 0 && records.length > 0) {
    targetElements = records
  }

  if (targetElements.length > 0) {
    console.log('找到XML行数据:', targetElements.length)

    // 限制处理的行数
    const maxRows = Math.min(targetElements.length, 500) // 最多处理500行
    console.log('将处理XML行数:', maxRows)

    // 标准行结构
    for (let i = 0; i < maxRows; i++) {
      const row = targetElements[i]
      const rowObject: any = {}

      Array.from(row.children).forEach((child) => {
        // 清理数据，避免特殊字符
        const cleanValue = (child.textContent || '').trim()
        if (cleanValue.length > 0) {
          rowObject[child.tagName] = cleanValue
        }
      })

      if (Object.keys(rowObject).length > 0) {
        jsonData.push(rowObject)
      }
    }
  } else {
    console.log('使用通用XML解析...')

    // 通用结构：提取所有叶子节点
    const allElements = xmlDoc.getElementsByTagName('*')
    const rowObject: any = {}
    let elementCount = 0

    for (let i = 0; i < allElements.length && elementCount < 100; i++) {
      // 限制元素数量
      const element = allElements[i]
      if (element.textContent && element.children.length === 0) {
        const cleanValue = element.textContent.trim()
        if (cleanValue.length > 0) {
          rowObject[element.tagName] = cleanValue
          elementCount++
        }
      }
    }

    if (Object.keys(rowObject).length > 0) {
      jsonData.push(rowObject)
    }
  }

  console.log('XML解析完成，有效数据行数:', jsonData.length)
  console.log('XML解析结果示例:', jsonData.slice(0, 2)) // 只显示前2行
  return jsonData
}

// JSON转Excel - 改进版本，添加数据验证和优化
const jsonToExcel = async (jsonData: any[]): Promise<Blob> => {
  console.log('开始将JSON转换为Excel...')

  if (!jsonData || jsonData.length === 0) {
    throw new Error('没有数据可转换')
  }

  // 限制数据量，避免生成过大的Excel文件
  const maxRows = Math.min(jsonData.length, 1000)
  const processData = jsonData.slice(0, maxRows)
  console.log(`处理数据行数: ${processData.length}/${jsonData.length}`)

  // 提取所有可能的列名，并限制列数
  const allKeys = new Set<string>()
  processData.forEach((row) => {
    Object.keys(row).forEach((key) => {
      // 过滤掉过长的键名，避免Excel显示问题
      if (key.length <= 50) {
        allKeys.add(key)
      }
    })
  })

  const headers = Array.from(allKeys).slice(0, 50) // 最多50列
  console.log('Excel表头数量:', headers.length)
  console.log('Excel表头:', headers.slice(0, 10)) // 只显示前10个

  // 构建工作表数据
  const worksheetData = []
  worksheetData.push(headers) // 添加表头

  // 添加数据行，并清理数据
  processData.forEach((row, index) => {
    const rowData = headers.map((header) => {
      const value = row[header] || ''
      // 清理数据，确保Excel兼容性
      const cleanValue = String(value).substring(0, 255) // Excel单元格最大长度限制
      return cleanValue
    })
    worksheetData.push(rowData)
  })

  console.log('工作表数据行数:', worksheetData.length)
  console.log('工作表数据列数:', headers.length)

  try {
    // 创建工作簿
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

    // 转换为Excel文件
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
      compression: true, // 启用压缩
    })

    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })

    console.log('Excel文件生成完成，大小:', blob.size)
    return blob
  } catch (error) {
    console.error('Excel生成错误:', error)
    throw new Error('Excel文件生成失败')
  }
}

const handleAdd = (row: any) => {
  tableData.value.push(row)
}

const btnLoading = ref<boolean>(false)

const { tableData, getTableData, loading } = usePageData(2)

const handleDelete = async (row: any) => {
  const hasFiles = checkForFiles(row)

  ElMessageBox.confirm(
    hasFiles
      ? '当前目录下有挂载资源，是否确定删除？'
      : '删除时同步删除该目录下的资源，是否确定删除？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    handleDel(row)
  })
}

const checkForFiles = (row: any): boolean => {
  if (row.fileCount && row.fileCount > 0) {
    return true
  }

  if (row.childVOList && row.childVOList.length) {
    for (const child of row.childVOList) {
      if (checkForFiles(child)) {
        return true
      }
    }
  }

  return false
}

const handleDel = (row: any) => {
  if (row.childVOList && row.childVOList.length) {
    row.childVOList.forEach((item: any) => {
      handleDel(item)
    })
  }
  row.editFlag = 4
}
</script>
<style lang="less" scoped>
.table-tree {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .table-tree-loading {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
  }
}

.custom-tree-node {
  height: 40px;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .node-label {
    max-width: 280px;
    width: 100%;
    min-width: 200px;
    .ellipseLine();
  }

  .node-list {
    display: flex;
    box-sizing: border-box;
    align-items: center;
  }

  .node-item {
    line-height: 40px;
    .ellipseLine();
  }
}

.node-title {
  padding-left: 24px;
  padding-right: 30px;
}

.CatalogConfig {
  :deep(.el-tree-node__content) {
    height: 40px;
  }

  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0 20px 20px 20px;

  .breadcrumb {
    height: 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    line-height: 40px;

    .link {
      :deep(.el-breadcrumb__inner) {
        color: #303133;
        font-weight: bold;
        cursor: pointer;

        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }

  .tableData {
    padding: 15px;
    flex: 1;
    overflow: auto;
    box-sizing: border-box;

    .btns {
      display: flex;
      justify-content: flex-end;

      .ElUpload {
        margin: 0 12px;
      }
    }
  }
}
</style>
